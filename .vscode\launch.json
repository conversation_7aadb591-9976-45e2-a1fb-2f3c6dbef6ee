{
    /* 
     * Requires the Rust Language Server (rust-analyzer) and Cortex-Debug extensions
     * https://marketplace.visualstudio.com/items?itemName=rust-lang.rust-analyzer
     * https://marketplace.visualstudio.com/items?itemName=marus25.cortex-debug
     */
    "version": "0.2.0",
    "configurations": [
        {
            "type": "cortex-debug",
            "request": "launch",
            "name": "Debug (QEMU)",
            "servertype": "qemu",
            "cwd": "${workspaceRoot}",
            "preLaunchTask": "Cargo Build (debug)",
            "runToEntryPoint": "main",
            "executable": "./target/thumbv7m-none-eabi/debug/spi_test",
            /* Run `cargo build --example hello` and uncomment this line to run semi-hosting example */
            //"executable": "./target/thumbv7m-none-eabi/debug/examples/hello",
            "cpu": "cortex-m3",
            "machine": "lm3s6965evb",
        },
        {
            /* Configuration for the STM32F303 Discovery board */
            "type": "cortex-debug",
            "request": "launch",
            "name": "Debug (OpenOCD)",
            "servertype": "openocd",
            "cwd": "${workspaceRoot}",
            "preLaunchTask": "Cargo Build (debug)",
            "runToEntryPoint": "main",
            "executable": "./target/thumbv7em-none-eabihf/debug/spi_test",
            /* Run `cargo build --example itm` and uncomment this line to run itm example */
            // "executable": "./target/thumbv7em-none-eabihf/debug/examples/itm",
            "device": "STM32F303VCT6",
            "configFiles": [
                "interface/stlink-v2-1.cfg",
                "target/stm32f3x.cfg"
            ],
            "svdFile": "${workspaceRoot}/.vscode/STM32F303.svd",
            "swoConfig": {
                "enabled": true,
                "cpuFrequency": 8000000,
                "swoFrequency": 2000000,
                "source": "probe",
                "decoders": [
                    { "type": "console", "label": "ITM", "port": 0 }
                ]
            }
        }
    ]
}