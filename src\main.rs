#![no_std]
#![no_main]

use display_interface_spi::SPIInterface;
use embassy_executor::Spawner;
use embassy_stm32::gpio::{Input, Level, Output, Pull, Speed};
use embassy_time::Delay;
use embedded_graphics::{
    image::{Image, ImageRaw},
    pixelcolor::BinaryColor,
    prelude::*,
};
use embedded_hal::delay::DelayNs;
use ssd1306::{Ssd1306, prelude::*, size::DisplaySize128x64};
use {defmt_rtt as _, panic_probe as _};

#[embassy_executor::main]
async fn main(_spawner: Spawner) {
    let p = embassy_stm32::init(Default::default());

    // 初始化GPIO引脚
    let dc = Output::new(p.PA8, Level::Low, Speed::VeryHigh);
    let mut res = Output::new(p.PB12, Level::Low, Speed::VeryHigh);
    let miso = Input::new(p.PB14, Pull::Up);
    let mosi = Output::new(p.PB15, Level::Low, Speed::VeryHigh);
    let sck = Output::new(p.PB13, Level::Low, Speed::VeryHigh);
    // 创建 blocking SPI 实例 (只发送，不接收)

    // Create bitbang SPI instance with 1MHz clock
    let mut spi =
        bitbang_hal::spi::SPI::new(bitbang_hal::spi::MODE_0, miso, mosi, sck, Delay, 1_000_000);

    let interface = SPIInterface::new(spi, dc);
    let mut display = Ssd1306::new(interface, DisplaySize128x64, DisplayRotation::Rotate0)
        .into_buffered_graphics_mode();

    display
        .reset(&mut res, &mut embassy_time::Delay {})
        .unwrap();

    display.init().unwrap();
    let raw: ImageRaw<BinaryColor> = ImageRaw::new(include_bytes!("./rust.raw"), 64);

    for i in (0..=64).chain((0..64).rev()).cycle() {
        let top_left = Point::new(i, 0);
        let im = Image::new(&raw, top_left);

        im.draw(&mut display).unwrap();

        display.flush().unwrap();

        display.clear(BinaryColor::Off).unwrap();
    }
}
