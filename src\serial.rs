//! Serial communication (USART)
//!
//! This implementation consumes the following hardware resources:
//! - Periodic timer to mark clock cycles
//! - Output GPIO pin for transmission (TX)
//! - Input GPIO pin for reception (RX)
//!
//! The timer must be configured to twice the desired communication frequency.
//!

use embedded_hal::delay::DelayNs;
use embedded_hal::digital::{InputPin, OutputPin};
use embedded_hal_nb::serial::{ErrorType, Read, Write};

/// Serial communication error type
#[derive(Debug)]
pub enum Error<E> {
    /// Bus error
    Bus(E),
}

impl<E: core::fmt::Debug> embedded_hal_nb::serial::Error for Error<E> {
    fn kind(&self) -> embedded_hal_nb::serial::ErrorKind {
        match self {
            Error::Bus(_) => embedded_hal_nb::serial::ErrorKind::Other,
        }
    }
}

/// Bit banging serial communication (USART) device
pub struct Serial<TX, RX, DELAY>
where
    TX: OutputPin,
    RX: InputPin,
    DELAY: DelayNs,
{
    tx: TX,
    rx: RX,
    delay: DELAY,
    bit_period_ns: u32,
}

impl<TX, RX, DELAY, E> Serial<TX, RX, DELAY>
where
    TX: OutputPin<Error = E>,
    RX: InputPin<Error = E>,
    DELAY: DelayNs,
    E: core::fmt::Debug,
{
    /// Create instance
    pub fn new(tx: TX, rx: RX, delay: DELAY, baud_rate: u32) -> Self {
        let bit_period_ns = 1_000_000_000 / baud_rate;
        Serial {
            tx,
            rx,
            delay,
            bit_period_ns,
        }
    }

    #[inline]
    fn wait_for_timer(&mut self) {
        self.delay.delay_ns(self.bit_period_ns);
    }
}

impl<TX, RX, DELAY, E> ErrorType for Serial<TX, RX, DELAY>
where
    TX: OutputPin<Error = E>,
    RX: InputPin<Error = E>,
    DELAY: DelayNs,
    E: core::fmt::Debug,
{
    type Error = crate::serial::Error<E>;
}

impl<TX, RX, DELAY, E> Write<u8> for Serial<TX, RX, DELAY>
where
    TX: OutputPin<Error = E>,
    RX: InputPin<Error = E>,
    DELAY: DelayNs,
    E: core::fmt::Debug,
{
    fn write(&mut self, byte: u8) -> nb::Result<(), Self::Error> {
        let mut data_out = byte;
        self.tx.set_low().map_err(Error::Bus)?; // start bit
        self.wait_for_timer();
        for _bit in 0..8 {
            if data_out & 1 == 1 {
                self.tx.set_high().map_err(Error::Bus)?;
            } else {
                self.tx.set_low().map_err(Error::Bus)?;
            }
            data_out >>= 1;
            self.wait_for_timer();
        }
        self.tx.set_high().map_err(Error::Bus)?; // stop bit
        self.wait_for_timer();
        Ok(())
    }

    fn flush(&mut self) -> nb::Result<(), Self::Error> {
        Ok(())
    }
}

impl<TX, RX, DELAY, E> Read<u8> for Serial<TX, RX, DELAY>
where
    TX: OutputPin<Error = E>,
    RX: InputPin<Error = E>,
    DELAY: DelayNs,
    E: core::fmt::Debug,
{
    fn read(&mut self) -> nb::Result<u8, Self::Error> {
        let mut data_in = 0;
        // wait for start bit
        while self.rx.is_high().map_err(Error::Bus)? {}
        self.wait_for_timer();
        for _bit in 0..8 {
            data_in <<= 1;
            if self.rx.is_high().map_err(Error::Bus)? {
                data_in |= 1
            }
            self.wait_for_timer();
        }
        // wait for stop bit
        self.wait_for_timer();
        Ok(data_in)
    }
}
